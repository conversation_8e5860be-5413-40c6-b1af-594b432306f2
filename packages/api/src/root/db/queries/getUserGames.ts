import { and, eq, isNotNull, ne, or } from "drizzle-orm"
import { undefined } from "zod"

import { BggGameDataExtracted, EventGameStatus } from "../../types/game.types"
import { db } from "../index"
import { gamesSchema } from "../schema/games.schema"
import { userToBaseGameSchema } from "../schema/userToBaseGame.schema"
import { userToGameToEventSchema } from "../schema/userToGameToEvent.schema"
import { usersSchema } from "../schema/users.schema"

// Define base select fields that are always present
const baseSelectFields = {
  id: gamesSchema.id,
  title: gamesSchema.title,
  bggId: gamesSchema.bggId,
  rating: userToBaseGameSchema.rating,
  lastPlay: userToBaseGameSchema.lastPlay,
  news: userToBaseGameSchema.news,
  playCount: userToBaseGameSchema.playCount,
  bggInfo: gamesSchema.bggInfo,
  lastUpdated: userToBaseGameSchema.lastUpdated,
  tags: userToBaseGameSchema.tags,
} as const

// Define select fields with event status for when eventId is provided
const selectFieldsWithEvent = {
  ...baseSelectFields,
  eventStatus: userToGameToEventSchema.status,
} as const

export async function getUserGames(userId: number, eventId?: number) {
  // Create the base query with proper joins first
  const baseQuery = db
    .select(baseSelectFields)
    .from(userToBaseGameSchema)
    .innerJoin(gamesSchema, eq(userToBaseGameSchema.gameId, gamesSchema.id))

  // Handle the conditional query based on whether eventId is provided
  const selectGames = eventId
    ? db
        .select(selectFieldsWithEvent)
        .from(userToBaseGameSchema)
        .innerJoin(gamesSchema, eq(userToBaseGameSchema.gameId, gamesSchema.id))
        .leftJoin(
          userToGameToEventSchema,
          and(
            eq(userToGameToEventSchema.userId, userId),
            eq(userToGameToEventSchema.gameId, gamesSchema.id),
            eq(userToGameToEventSchema.eventId, eventId),
          ),
        )
    : baseQuery

  return await selectGames
    .where(
      and(
        eq(userToBaseGameSchema.userId, userId),
        isNotNull(gamesSchema.bggInfo),
      ),
    )
    .then(async (games) => {
      return await Promise.all(
        games.map(async (game) => {
          let bggDataExtracted = {} as unknown as BggGameDataExtracted
          try {
            bggDataExtracted = JSON.parse(
              game.bggInfo ?? "{}",
            ) as BggGameDataExtracted
          } catch (e: unknown) {
            console.error(
              "Error while parsing bgg info:",
              e,
              `Info: ${game.bggInfo}`,
              `Game id: ${game.id}`,
            )
          }

          let tags: number[] = []

          try {
            tags =
              game.tags && game.tags.length > 0
                ? (JSON.parse(game.tags ?? "[]") as number[])
                : []
          } catch (e: unknown) {
            console.error(
              "Error while parsing tags:",
              e,
              `Tags: ${game.tags}`,
              `Game id: ${game.id}`,
            )
          }

          const otherUserStatus = eventId
            ? await db
                .select({
                  status: userToGameToEventSchema.status,
                  name: usersSchema.name,
                  avatar: usersSchema.avatar,
                  color: usersSchema.color,
                  id: usersSchema.id,
                })
                .from(userToGameToEventSchema)
                .innerJoin(
                  usersSchema,
                  eq(userToGameToEventSchema.userId, usersSchema.id),
                )
                .where(
                  and(
                    ne(userToGameToEventSchema.userId, userId),
                    eq(userToGameToEventSchema.gameId, game.id),
                    eq(userToGameToEventSchema.eventId, eventId),
                    or(
                      eq(userToGameToEventSchema.status, "willbring"),
                      eq(userToGameToEventSchema.status, "wish"),
                      eq(userToGameToEventSchema.status, "willplay"),
                    ),
                  ),
                )
                .then((res) => res)
            : null

          // For TypeScript: eventStatus is either from the join (when eventId is provided) or null
          const eventStatus: EventGameStatus | null =
            eventId && "eventStatus" in game
              ? (game.eventStatus as EventGameStatus | null)
              : null

          return {
            ...game,
            tags,
            bggInfo: undefined,
            eventStatus,
            otherUserStatus,
            players: {
              box: {
                min: parseInt(bggDataExtracted.players?.box?.min ?? "0"),
                max: parseInt(bggDataExtracted.players?.box?.max ?? "0"),
              },
              stats: bggDataExtracted.players?.stats?.map((stat) => [
                parseInt(stat?.players ?? "0"),
                stat?.status ?? 1,
              ]),
            },
            weight: parseInt(bggDataExtracted.averageweight ?? "0"),
            average:
              Math.round(parseFloat(bggDataExtracted.average ?? "0") * 100) /
              100,
          }
        }),
      )
    })
}
