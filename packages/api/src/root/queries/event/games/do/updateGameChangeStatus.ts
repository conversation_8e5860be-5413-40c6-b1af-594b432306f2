import { z } from "zod"

import { OK_RESPONSE } from "../../../../helpers/responses"
import { eventProcedure } from "../../../../trpc/procedures/eventProcedure"
import { setGameState } from "../../helpers/setGameState"

export const updateGameChangeStatus = eventProcedure
  .input(
    z.object({
      eventId: z.number(),
      gameId: z.number(),
      status: z.enum([
        "wish",
        "willbring",
        "canask",
        "willnotbring",
        "willplay",
      ]),
    }),
  )
  .mutation(async ({ input, ctx: { loginData, event } }) => {
    await setGameState({
      userId: loginData.id,
      eventId: event.id,
      gameId: input.gameId,
      status: input.status,
    })

    return OK_RESPONSE
  })
