import type { LinkComponentProps } from "@tanstack/react-router"

export interface ICThumbnailGame {
  id: number
  title: string
  bggId: number
  rating?: number | null
  average?: number | null
  playCount?: number | null
  lastPlay?: string | null
  eventStatus?: string | null
  otherUserStatus?:
    | {
        status: string | null
        name: string | null
        avatar: string | null
        color: string | null
        id: number | null
      }[]
    | null
}

export type ICNavigationProps = Pick<
  LinkComponentProps,
  "to" | "search" | "params"
>
