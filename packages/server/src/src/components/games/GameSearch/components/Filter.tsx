import BackpackIcon from "@mui/icons-material/Backpack"
import CloseIcon from "@mui/icons-material/Close"
import QuestionMarkIcon from "@mui/icons-material/QuestionMark"
import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  type SelectChangeEvent,
  Typography,
} from "@mui/material"
import { useCallback } from "react"

import { EventWizzardFilter } from "../../../../types/types"

type FilterProps = {
  isEventWizzard?: boolean
  onChange: (filter: EventWizzardFilter | -1 | undefined) => void
  filter?: string | undefined | -1
}

type FilterOption = {
  key: EventWizzardFilter
  title: string
  label: React.ReactNode
}

export const Filter = ({ isEventWizzard, onChange, filter }: FilterProps) => {
  const filterOptions: FilterOption[] = []

  if (isEventWizzard) {
    filterOptions.push({
      key: "event-wizzard-willbring",
      title: "Show only games I will bring",
      label: <BackpackIcon color="info" />,
    })
    filterOptions.push({
      key: "event-wizzard-non-cancelled",
      label: (
        <Box display="flex" flexDirection="row" gap={1}>
          <BackpackIcon color="info" />
          <Typography>/</Typography>
          <QuestionMarkIcon color="inherit" />
        </Box>
      ),
      title: "Hide games I will not take",
    })
    filterOptions.push({
      key: "event-wizzard-cancelled",
      label: (
        <Box display="flex" flexDirection="row" gap={1}>
          <CloseIcon color="error" />
        </Box>
      ),
      title: "Hide games I will not take",
    })
  }

  const handleSelectFilter = useCallback(
    (e: SelectChangeEvent<unknown>) => {
      onChange(e.target.value as EventWizzardFilter)
    },
    [onChange],
  )

  if (filterOptions.length === 0) {
    return null
  }

  return (
    <FormControl>
      <InputLabel id={`filter-games-label-id`}>Filter</InputLabel>
      <Select
        label="Filter"
        labelId={`filter-games-label-id`}
        onChange={handleSelectFilter}
        value={filter ?? -1}
      >
        <MenuItem value={-1} key="none">
          None
        </MenuItem>
        {filterOptions.map((item) => (
          <MenuItem key={item.key} value={item.key} title={item.title}>
            {item.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  )
}
