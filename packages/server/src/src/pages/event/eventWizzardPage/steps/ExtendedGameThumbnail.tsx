import BackpackIcon from "@mui/icons-material/Backpack"
import CloseIcon from "@mui/icons-material/Close"
import PlayCircleOutlineIcon from "@mui/icons-material/PlayCircleOutline"
import QuestionMarkIcon from "@mui/icons-material/QuestionMark"
import { Box, FormControlLabel, Radio, RadioGroup } from "@mui/material"
import classnames from "classnames"
import { useEffect, useState } from "react"

import { ICThumbnailGame } from "../../../../components/games/GameThumbnail/type"
import { UserAvatarGroup } from "../../../../components/user/UserAvatarGroup/UserAvatarGroup"

import * as styles from "./extendedGameThumbnail.module.css"

type ExtendedGameThumbnailProps = {
  game: ICThumbnailGame
  canRequest: boolean
  onChange: (gameId: number, value: string) => void
}
export const ExtendedGameThumbnail = ({
  game,
  canRequest,
  onChange,
}: ExtendedGameThumbnailProps) => {
  const [status, setStatus] = useState<string | null>(
    game.eventStatus ?? (canRequest ? "canask" : "willnotbring"),
  )

  useEffect(() => {
    setStatus(game.eventStatus ?? "canask")
  }, [game.eventStatus])

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setStatus(event.target.value)
    onChange(game.id, event.target.value)
  }

  const otherUsers = game.otherUserStatus ?? []

  const sortedUsers =
    otherUsers.length > 0
      ? otherUsers.reduce<Record<string, typeof otherUsers>>((acc, user) => {
          const key = user.status
          if (!acc[key]) acc[key] = []
          acc[key].push(user)
          return acc
        }, {})
      : {
          wish: [],
          willbring: [],
          willplay: [],
          canask: [],
          willnotbring: [],
        }

  const wish = {
    ...(sortedUsers?.wish ?? []),
    ...(sortedUsers?.willplay ?? []),
  }

  return (
    <Box
      className={classnames(styles.container, {
        [styles.containerNo]: status === "willnotbring",
        [styles.containerYes]: status === "willbring",
      })}
    >
      <RadioGroup
        className={styles.radioGroup}
        value={status}
        row
        name="game-option"
        onChange={handleChange}
      >
        <FormControlLabel
          labelPlacement="bottom"
          color="secondary"
          slotProps={{
            typography: { variant: "body1", color: "info", fontWeight: 600 },
          }}
          value="willbring"
          control={<Radio color="info" size="small" />}
          label={<BackpackIcon color="info" />}
          title="I will take this game"
        />
        <FormControlLabel
          labelPlacement="bottom"
          value="canask"
          slotProps={{
            typography: { variant: "body1", fontWeight: 600 },
          }}
          control={<Radio color="default" size="small" />}
          label={<QuestionMarkIcon />}
          title="I will take the game if someone asks for it"
        />
        <FormControlLabel
          labelPlacement="bottom"
          title="I don't want to take this game"
          slotProps={{
            typography: { variant: "body1", color: "error", fontWeight: 600 },
          }}
          value="willnotbring"
          control={<Radio color="error" size="small" />}
          label={<CloseIcon color="error" />}
        />
      </RadioGroup>
      {(game.otherUserStatus ?? []).length > 0 && (
        <Box
          display="flex"
          flexDirection="row"
          gap={2}
          width="100%"
          borderTop="1px solid #ccc"
          padding={1}
          boxSizing="border-box"
          alignItems="center"
          justifyContent="center"
        >
          {sortedUsers.willbring?.length > 0 && (
            <Box display="flex" flexDirection="row" gap={2} title="Will bring">
              <BackpackIcon color="info" />
              <UserAvatarGroup users={sortedUsers.willbring} />
            </Box>
          )}
          {wish?.length > 0 && (
            <Box
              display="flex"
              flexDirection="row"
              gap={2}
              title="Would like to play"
            >
              <PlayCircleOutlineIcon color="info" />
              <UserAvatarGroup users={wish} />
            </Box>
          )}
        </Box>
      )}
    </Box>
  )
}
